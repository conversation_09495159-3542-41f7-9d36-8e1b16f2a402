{"pages": ["pages/index/index", "pages/recipe/recipe", "pages/recipe-detail/recipe-detail", "pages/history/history", "pages/profile/profile", "pages/camera/camera", "pages/recognition-result/recognition-result", "pages/favorites/favorites", "pages/settings/settings"], "window": {"backgroundTextStyle": "light", "backgroundColor": "#fff"}, "tabBar": {"color": "#999", "selectedColor": "#ff6b6b", "backgroundColor": "#fff", "borderStyle": "black", "list": [{"pagePath": "pages/index/index", "text": "首页"}, {"pagePath": "pages/history/history", "text": "历史"}, {"pagePath": "pages/profile/profile", "text": "我的"}]}, "style": "v2", "renderer": "skyline", "rendererOptions": {"skyline": {"defaultDisplayBlock": true, "defaultContentBox": true, "disableABTest": true, "sdkVersionBegin": "3.0.0", "sdkVersionEnd": "15.255.255"}}, "componentFramework": "glass-easel", "sitemapLocation": "sitemap.json", "lazyCodeLoading": "requiredComponents", "usingComponents": {"navigation-bar": "/components/navigation-bar/navigation-bar"}}