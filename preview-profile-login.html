<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户登录页面预览</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #FFF6F0;
            line-height: 1.6;
        }
        
        .container {
            max-width: 375px;
            margin: 0 auto;
            background: #FFF6F0;
            min-height: 100vh;
            padding: 20px 16px;
        }
        
        .profile-header-spacer {
            height: 60px;
        }
        
        .profile-header {
            display: flex;
            flex-direction: column;
            align-items: center;
            margin-bottom: 30px;
        }
        
        .avatar {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            margin-bottom: 10px;
        }
        
        .avatar-placeholder {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            margin-bottom: 10px;
            background-color: #f0f0f0;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .avatar-icon {
            font-size: 40px;
            color: #ccc;
        }
        
        .nickname {
            font-size: 16px;
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }
        
        .nickname-placeholder {
            font-size: 16px;
            color: #999;
            margin-bottom: 5px;
        }
        
        .btn-login {
            margin-top: 10px;
            padding: 12px 20px;
            background: linear-gradient(90deg, #ff7e2d 0%, #ff3d2d 100%);
            color: #fff;
            border-radius: 20px;
            font-size: 14px;
            border: none;
            box-shadow: 0 2px 6px rgba(255, 61, 45, 0.3);
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .btn-login:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(255, 61, 45, 0.4);
        }
        
        .btn-login:disabled {
            background: #ccc;
            box-shadow: none;
            cursor: not-allowed;
            transform: none;
        }
        
        .btn-logout {
            margin-top: 10px;
            padding: 12px 20px;
            background: #f5f5f5;
            color: #666;
            border-radius: 20px;
            font-size: 14px;
            border: 1px solid #ddd;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .btn-logout:hover {
            background: #e8e8e8;
        }
        
        .section {
            margin: 30px 0;
        }
        
        .section-header {
            margin-bottom: 10px;
        }
        
        .section-title {
            font-size: 16px;
            font-weight: bold;
            color: #333;
        }
        
        .favorite-list {
            width: 100%;
        }
        
        .favorite-item {
            display: flex;
            align-items: center;
            padding: 10px;
            background: #fff;
            border-radius: 4px;
            box-shadow: 0 1px 2px rgba(0,0,0,0.1);
            margin-bottom: 10px;
        }
        
        .favorite-image {
            width: 50px;
            height: 50px;
            border-radius: 4px;
            margin-right: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 30px;
            background-color: #f5f5f5;
        }
        
        .favorite-info {
            flex: 1;
        }
        
        .favorite-name {
            font-size: 14px;
            color: #333;
            margin-bottom: 3px;
            display: block;
        }
        
        .favorite-meta {
            display: flex;
            gap: 10px;
            font-size: 12px;
            color: #666;
        }
        
        .favorite-btn {
            padding: 5px;
            color: #ff6b6b;
            font-size: 14px;
            cursor: pointer;
        }
        
        .empty-state {
            text-align: center;
            padding: 20px;
            color: #999;
            font-size: 14px;
        }
        
        .demo-toggle {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 8px 16px;
            background: #007aff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
        }
        
        .loading {
            opacity: 0.6;
            pointer-events: none;
        }
    </style>
</head>
<body>
    <button class="demo-toggle" onclick="toggleLoginState()">切换登录状态</button>
    
    <div class="container">
        <div class="profile-header-spacer"></div>
        
        <!-- 未登录状态 -->
        <div id="logged-out" class="profile-header">
            <div class="avatar-placeholder">
                <span class="avatar-icon">👤</span>
            </div>
            <span class="nickname-placeholder">未登录</span>
            <button class="btn-login" onclick="simulateLogin()" id="loginBtn">
                微信登录
            </button>
        </div>
        
        <!-- 已登录状态 -->
        <div id="logged-in" class="profile-header" style="display: none;">
            <img class="avatar" src="https://thirdwx.qlogo.cn/mmopen/vi_32/POgEwh4mIHO4nibH0KlMECNjjGxQUq24ZEaGT4poC6icRiccVGKSyXwibcPq4BWmiaIGuG1icwxaQX6grC9VemZoJ8rg/132" alt="用户头像" />
            <span class="nickname">微信用户</span>
            <button class="btn-logout" onclick="simulateLogout()">
                退出登录
            </button>
        </div>
        
        <!-- 收藏夹部分 -->
        <div class="section" id="favorites-section" style="display: none;">
            <div class="section-header">
                <span class="section-title">收藏夹</span>
            </div>
            <div class="favorite-list">
                <div class="favorite-item">
                    <span class="favorite-image">🥬</span>
                    <div class="favorite-info">
                        <span class="favorite-name">清炒时蔬</span>
                        <div class="favorite-meta">
                            <span class="favorite-time">15分钟</span>
                            <span class="favorite-difficulty">简单</span>
                        </div>
                    </div>
                    <div class="favorite-btn">♥</div>
                </div>
                <div class="favorite-item">
                    <span class="favorite-image">🍖</span>
                    <div class="favorite-info">
                        <span class="favorite-name">红烧排骨</span>
                        <div class="favorite-meta">
                            <span class="favorite-time">45分钟</span>
                            <span class="favorite-difficulty">中等</span>
                        </div>
                    </div>
                    <div class="favorite-btn">♥</div>
                </div>
            </div>
        </div>
        
        <!-- 空状态 -->
        <div class="section" id="empty-section">
            <div class="empty-state">
                <span class="empty-text">登录后可查看收藏的菜品</span>
            </div>
        </div>
    </div>
    
    <script>
        let isLoggedIn = false;
        
        function toggleLoginState() {
            if (isLoggedIn) {
                simulateLogout();
            } else {
                simulateLogin();
            }
        }
        
        function simulateLogin() {
            // 模拟微信授权弹窗
            const userConfirm = confirm('微信授权\n\n"智能菜谱助手"申请获取以下权限：\n• 获得你的公开信息（昵称、头像等）\n\n用于完善用户资料');

            if (!userConfirm) {
                // 用户拒绝授权
                alert('需要授权才能登录');
                return;
            }

            // 用户同意授权，开始登录流程
            const loginBtn = document.getElementById('loginBtn');
            loginBtn.textContent = '登录中...';
            loginBtn.classList.add('loading');
            loginBtn.disabled = true;

            // 模拟登录延迟
            setTimeout(() => {
                document.getElementById('logged-out').style.display = 'none';
                document.getElementById('logged-in').style.display = 'flex';
                document.getElementById('favorites-section').style.display = 'block';
                document.getElementById('empty-section').style.display = 'none';

                isLoggedIn = true;

                // 重置按钮状态
                loginBtn.textContent = '微信登录';
                loginBtn.classList.remove('loading');
                loginBtn.disabled = false;

                // 显示登录成功提示
                setTimeout(() => {
                    alert('登录成功！');
                }, 100);
            }, 1500);
        }
        
        function simulateLogout() {
            document.getElementById('logged-out').style.display = 'flex';
            document.getElementById('logged-in').style.display = 'none';
            document.getElementById('favorites-section').style.display = 'none';
            document.getElementById('empty-section').style.display = 'block';
            
            isLoggedIn = false;
        }
    </script>
</body>
</html>
