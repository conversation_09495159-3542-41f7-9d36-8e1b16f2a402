/**app.wxss**/
page {
  --primary-color: #4CAF50;
  --secondary-color: #FF9800;
  --background-color: #F5F5F5;
  --text-color: #333333;
  --text-color-secondary: #666666;
  --border-color: #EEEEEE;
  --success-color: #4CAF50;
  --warning-color: #FF9800;
  --error-color: #F44336;
  
  background-color: var(--background-color);
  font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica,
    Segoe UI, Arial, Roboto, 'PingFang SC', 'miui', 'Hiragino Sans GB', 'Microsoft Yahei',
    sans-serif;
  font-size: 28rpx;
  color: var(--text-color);
  line-height: 1.5;
}

/* 通用容器 */
.container {
  padding: 30rpx;
  box-sizing: border-box;
}

/* 卡片样式 */
.card {
  background: #FFFFFF;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

/* 按钮样式 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 20rpx 40rpx;
  border-radius: 40rpx;
  font-size: 28rpx;
  font-weight: 500;
  transition: all 0.3s ease;
}

.btn-primary {
  background-color: var(--primary-color);
  color: #FFFFFF;
}

.btn-secondary {
  background-color: var(--secondary-color);
  color: #FFFFFF;
}

.btn-outline {
  border: 2rpx solid var(--primary-color);
  color: var(--primary-color);
  background-color: transparent;
}

/* 文本样式 */
.text-primary {
  color: var(--primary-color);
}

.text-secondary {
  color: var(--secondary-color);
}

.text-gray {
  color: var(--text-color-secondary);
}

.text-center {
  text-align: center;
}

.text-bold {
  font-weight: bold;
}

/* 间距工具类 */
.mt-10 { margin-top: 10rpx; }
.mt-20 { margin-top: 20rpx; }
.mt-30 { margin-top: 30rpx; }
.mb-10 { margin-bottom: 10rpx; }
.mb-20 { margin-bottom: 20rpx; }
.mb-30 { margin-bottom: 30rpx; }

/* 弹性布局 */
.flex {
  display: flex;
}

.flex-column {
  flex-direction: column;
}

.flex-center {
  justify-content: center;
  align-items: center;
}

.flex-between {
  justify-content: space-between;
}

.flex-around {
  justify-content: space-around;
}

.flex-wrap {
  flex-wrap: wrap;
}

/* 图片样式 */
.img-cover {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.img-contain {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

/* 动画类 */
.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* 加载状态 */
.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 30rpx;
}

.loading-text {
  color: var(--text-color-secondary);
  font-size: 24rpx;
  margin-left: 10rpx;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx 30rpx;
}

.empty-state-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 20rpx;
}

.empty-state-text {
  color: var(--text-color-secondary);
  font-size: 28rpx;
}
