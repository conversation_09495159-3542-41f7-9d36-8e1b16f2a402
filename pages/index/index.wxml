<!--index.wxml-->
<navigation-bar title="首页" back="{{false}}" color="black" background="#FFF"></navigation-bar>
<scroll-view class="scrollarea" scroll-y type="list">
  <view class="container">
    <!-- 拍照识别卡片 -->
    <view class="camera-card">
      <view class="camera-icon-bg">
        <text class="camera-icon-large">📷</text>
      </view>
      <text class="camera-title">拍照识别食材</text>
      <text class="camera-desc">对着家里的食材拍一张照片</text>
      <button class="camera-btn" bindtap="navigateToCamera">开始拍照</button>
    </view>

    <!-- 功能区块 -->
    <view class="feature-row">
      <view class="feature-card" bindtap="onSearch">
        <text class="feature-icon">🔍</text>
        <text class="feature-title">搜索食谱</text>
        <text class="feature-desc">按菜名搜索菜谱</text>
      </view>
      <view class="feature-card" bindtap="navigateToFavorite">
        <text class="feature-icon">❤</text>
        <text class="feature-title">我的收藏</text>
        <text class="feature-desc">保存喜欢的菜谱</text>
      </view>
    </view>

    <!-- 今日推荐 -->
    <view class="section">
      <view class="section-header">
        <text class="section-title">今日推荐</text>
      </view>
      <view class="recipe-list">
        <view class="recipe-item" wx:for="{{recommendRecipes}}" wx:key="id" bindtap="navigateToDetail" data-id="{{item.id}}">
          <view class="recipe-img-area">
            <text class="recipe-img">🍲</text>
          </view>
          <view class="recipe-info-area">
            <view class="recipe-title-row">
              <text class="recipe-name">{{item.name}}</text>
              <text class="meta-star">⭐ {{item.difficultyLevel}}</text>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</scroll-view>
