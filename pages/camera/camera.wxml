<!--pages/camera/camera.wxml-->
<navigation-bar title="拍照识别食材" back="{{true}}" color="black" background="#FFF"></navigation-bar>
<view class="container">
  <!-- 添加顶部间距 -->
  <view class="header-spacer"></view>

  <!-- 开发环境提示 -->
  <view class="dev-tools-tip" wx:if="{{isDevTools}}">
    <text class="tip-text">相机功能需要在真机上预览调试</text>
    <text class="tip-subtext">请使用手机微信扫码预览</text>
  </view>

  <!-- 相机预览区域 -->
  <view class="camera-container" wx:if="{{hasCameraAuth && !isDevTools}}">
    <camera 
      device-position="{{devicePosition}}"
      flash="{{flashMode}}"
      binderror="handleCameraError"
      class="camera"
      mode="normal"
      frame-size="medium"
      style="width: 100%; height: 100%;"
    >
      <!-- 取景框 -->
      <view class="viewfinder">
        <view class="corner top-left"></view>
        <view class="corner top-right"></view>
        <view class="corner bottom-left"></view>
        <view class="corner bottom-right"></view>
      </view>
    </camera>

    <!-- 已拍摄照片预览 -->
    <view class="photos-preview" wx:if="{{photos.length > 0}}">
      <scroll-view scroll-x class="photos-scroll">
        <view class="photos-list">
          <view class="photo-item" wx:for="{{photos}}" wx:key="timestamp">
            <image 
              src="{{item.path}}" 
              mode="aspectFill" 
              bindtap="previewPhoto" 
              data-index="{{index}}"
            />
            <view class="photo-delete" bindtap="deletePhoto" data-index="{{index}}">×</view>
          </view>
        </view>
      </scroll-view>
    </view>
  </view>

  <!-- 无权限提示 -->
  <view class="no-auth-container" wx:else>
    <text class="no-auth-text">请授权相机权限以使用拍照功能</text>
    <button 
      class="auth-btn {{isAuthBtnPressed ? 'pressed' : ''}}" 
      bindtap="checkCameraAuth"
      bindtouchstart="onAuthBtnTouchStart"
      bindtouchend="onAuthBtnTouchEnd"
    >重新授权</button>
  </view>

  <!-- 底部操作区 -->
  <view class="footer">
    <!-- 取消按钮 -->
    <view class="action-btn" bindtap="cancelPhotos" wx:if="{{photos.length > 0}}">
      <text class="action-icon">❌</text>
      <text class="action-text">取消</text>
    </view>

    <!-- 相册选择 -->
    <view class="action-btn" bindtap="chooseFromAlbum" wx:else>
      <text class="action-icon">🖼️</text>
      <text class="action-text">相册</text>
    </view>

    <!-- 拍照按钮 -->
    <view 
      class="capture-btn {{isCaptureBtnPressed ? 'pressed' : ''}}" 
      bindtap="takePhoto"
      bindtouchstart="onCaptureBtnTouchStart"
      bindtouchend="onCaptureBtnTouchEnd"
    >
      <view class="capture-btn-inner"></view>
    </view>

    <!-- 上传按钮 -->
    <view 
      class="action-btn {{isUploading ? 'disabled' : ''}}" 
      bindtap="uploadPhotos" 
      wx:if="{{photos.length > 0}}"
    >
      <text class="action-icon">⬆️</text>
      <text class="action-text">上传</text>
    </view>

    <!-- 切换摄像头 -->
    <view class="action-btn" bindtap="toggleCamera" wx:else>
      <text class="action-icon">🔄</text>
      <text class="action-text">切换摄像头</text>
    </view>
  </view>

  <!-- 上传进度 -->
  <view class="upload-progress" wx:if="{{isUploading}}">
    <view class="progress-bar">
      <view class="progress-inner" style="width: {{uploadProgress}}%"></view>
    </view>
    <text class="progress-text">上传中 {{uploadProgress}}%</text>
  </view>

  <!-- 识别结果弹窗 -->
  <view class="result-modal {{showResult ? 'show' : ''}}" wx:if="{{showResult}}">
    <view class="modal-content">
      <view class="modal-header">
        <text class="modal-title">识别结果</text>
        <view class="close-btn" bindtap="closeResult">×</view>
      </view>
      
      <!-- 识别到的食材列表 -->
      <scroll-view class="ingredients-list" scroll-y>
        <view class="ingredient-item" wx:for="{{ingredients}}" wx:key="id">
          <image class="ingredient-image" src="{{item.image}}" mode="aspectFill" />
          <view class="ingredient-info">
            <text class="ingredient-name">{{item.name}}</text>
            <text class="ingredient-confidence">可信度: {{item.confidence}}%</text>
          </view>
          <view class="ingredient-actions">
            <view class="action-icon edit" bindtap="editIngredient" data-id="{{item.id}}">
              <text>✏️</text>
            </view>
            <view class="action-icon delete" bindtap="deleteIngredient" data-id="{{item.id}}">
              <text>❌</text>
            </view>
          </view>
        </view>
      </scroll-view>

      <!-- 底部按钮 -->
      <view class="modal-footer">
        <button class="btn btn-outline" bindtap="retakePhoto">重新拍摄</button>
        <button class="btn btn-primary" bindtap="confirmIngredients">确认食材</button>
      </view>
    </view>
  </view>

  <!-- 加载提示 -->
  <view class="loading-mask" wx:if="{{isLoading}}">
    <view class="loading-content">
      <text class="loading-icon">⏳</text>
      <text class="loading-text">正在识别食材...</text>
    </view>
  </view>
</view>