// pages/camera/camera.js
const app = getApp();
const apiConfig = require('../../config/api.js');

Page({

  /**
   * 页面的初始数据
   */
  data: {
    devicePosition: 'back',
    showResult: false,
    isLoading: false,
    ingredients: [],
    flashMode: 'auto',
    hasCameraAuth: false,
    isAuthBtnPressed: false,
    isCaptureBtnPressed: false,
    isDevTools: false,
    photos: [], // 存储拍摄的照片
    isUploading: false, // 是否正在上传
    uploadProgress: 0, // 上传进度
    isTakingPhoto: false, // 防止重复拍照
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // 检测是否在开发者工具中
    const systemInfo = wx.getSystemInfoSync();
    this.setData({
      isDevTools: systemInfo.platform === 'devtools'
    });

    if (this.data.isDevTools) {
      wx.showModal({
        title: '提示',
        content: '相机功能需要在真机上预览调试，请使用手机微信扫码预览',
        showCancel: false,
        success: () => {
          wx.navigateBack();
        }
      });
      return;
    }

    this.checkCameraAuth()
  },

  // 检查相机权限
  checkCameraAuth() {
    wx.getSetting({
      success: (res) => {
        if (!res.authSetting['scope.camera']) {
          wx.authorize({
            scope: 'scope.camera',
            success: () => {
              console.log('相机权限已获取')
              this.setData({ hasCameraAuth: true })
            },
            fail: () => {
              wx.showModal({
                title: '提示',
                content: '需要相机权限才能使用拍照功能，是否去设置？',
                success: (res) => {
                  if (res.confirm) {
                    wx.openSetting({
                      success: (settingRes) => {
                        if (settingRes.authSetting['scope.camera']) {
                          this.setData({ hasCameraAuth: true })
                        } else {
                          wx.navigateBack()
                        }
                      }
                    })
                  } else {
                    wx.navigateBack()
                  }
                }
              })
            }
          })
        } else {
          this.setData({ hasCameraAuth: true })
        }
      }
    })
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  },

  // 切换摄像头（前置/后置）
  toggleCamera() {
    const newPosition = this.data.devicePosition === 'back' ? 'front' : 'back';
    this.setData({ devicePosition: newPosition });
  },

  // 拍照
  takePhoto() {
    if (this.data.isTakingPhoto) return;
    this.setData({ isTakingPhoto: true });
    const camera = wx.createCameraContext();
    camera.takePhoto({
      quality: 'high',
      success: (res) => {
        // 将新照片添加到数组
        const photos = [...this.data.photos, {
          path: res.tempImagePath,
          timestamp: new Date().getTime()
        }];
        this.setData({ photos });
        wx.showToast({
          title: `已拍摄 ${photos.length} 张照片`,
          icon: 'none'
        });
      },
      fail: (error) => {
        console.error('拍照失败：', error);
        wx.showToast({
          title: '拍照失败',
          icon: 'none'
        });
      },
      complete: () => {
        this.setData({ isTakingPhoto: false });
      }
    });
  },

  // 从相册选择
  chooseFromAlbum() {
    wx.chooseMedia({
      count: 1,
      mediaType: ['image'],
      sourceType: ['album'],
      success: (res) => {
        this.setData({ isLoading: true })
        // 上传图片进行识别
        this.uploadAndRecognize(res.tempFiles[0].tempFilePath)
      }
    })
  },

  // 上传图片并识别（从相册选择的单张图片）
  async uploadAndRecognize(tempFilePath) {
    try {
      // 获取文件信息
      const fileInfo = await this.getFileInfo(tempFilePath);
      const base64 = await this.fileToBase64(tempFilePath);
      const timestamp = new Date().getTime();

      // 构造请求数据
      const uploadData = {
        files: [{
          fileName: `album_photo_${timestamp}.jpg`,
          fileId: timestamp,
          fileSize: fileInfo.size,
          fileData: `data:image/jpeg;base64,${base64}`
        }]
      };

      // 发送识别请求
      wx.request({
        url: `${apiConfig.baseUrl}${apiConfig.api.uploadPhotos}`,
        method: 'POST',
        data: uploadData,
        header: {
          'content-type': 'application/json'
        },
        success: (res) => {
          console.log('识别响应:', res);
          if (res.statusCode === 200 && res.data && res.data.code === 0) {
            // 跳转到识别结果页面
            wx.navigateTo({
              url: '/pages/recognition-result/recognition-result?result=' + encodeURIComponent(JSON.stringify(res.data.data))
            });
          } else {
            console.error('识别失败，状态码:', res.statusCode);
            wx.showToast({
              title: '识别失败，请重试',
              icon: 'none'
            });
          }
          this.setData({ isLoading: false });
        },
        fail: (error) => {
          console.error('识别失败:', error);
          wx.showToast({
            title: '识别失败，请重试',
            icon: 'none'
          });
          this.setData({ isLoading: false });
        }
      });
    } catch (error) {
      console.error('处理图片失败:', error);
      wx.showToast({
        title: '处理图片失败',
        icon: 'none'
      });
      this.setData({ isLoading: false });
    }
  },

  // 切换闪光灯
  toggleFlash() {
    const modes = ['auto', 'on', 'off']
    const currentIndex = modes.indexOf(this.data.flashMode)
    const nextIndex = (currentIndex + 1) % modes.length
    this.setData({
      flashMode: modes[nextIndex]
    })
  },

  // 编辑食材
  editIngredient(e) {
    const id = e.currentTarget.dataset.id;
    const index = this.data.ingredients.findIndex(item => item.id === id);
    if (index === -1) return;
    wx.showModal({
      title: "编辑食材",
      editable: true,
      placeholderText: "请输入食材名称",
      success: (res) => {
        if (res.confirm && res.content) {
           const ingredients = this.data.ingredients;
           ingredients[index].name = res.content;
           ingredients[index].confidence = 100;
           this.setData({ ingredients });
        }
      }
    });
  },

  // 删除食材
  deleteIngredient(e) {
    const id = e.currentTarget.dataset.id;
    wx.showModal({
      title: "删除食材",
      content: "确定要删除该食材吗？",
      success: (res) => {
        if (res.confirm) {
           const ingredients = this.data.ingredients.filter(item => item.id !== id);
           this.setData({ ingredients });
        }
      }
    });
  },

  // 关闭结果弹窗
  closeResult() {
    this.setData({ showResult: false })
  },

  // 重新拍摄
  retakePhoto() {
    this.setData({
      showResult: false,
      ingredients: []
    })
  },

  // 确认食材并跳转到菜谱推荐
  confirmIngredients() {
    if (this.data.ingredients.length === 0) {
      wx.showToast({
        title: '请至少识别一种食材',
        icon: 'none'
      })
      return
    }

    // 将食材数据传递到菜谱推荐页面
    const ingredients = this.data.ingredients.map(item => item.name)
    wx.navigateTo({
      url: `/pages/recipe/recipe?ingredients=${encodeURIComponent(JSON.stringify(ingredients))}`
    })
  },

  // 相机错误处理
  handleCameraError(e) {
    console.error('相机错误：', e.detail)
    const errMsg = e.detail.errMsg || '';
    
    // 处理特定错误
    if (errMsg.includes('not support')) {
      wx.showModal({
        title: '提示',
        content: '当前设备不支持相机功能，请使用其他设备',
        showCancel: false,
        success: () => {
          wx.navigateBack();
        }
      });
      return;
    }

    // 处理其他错误
    wx.showModal({
      title: '相机启动失败',
      content: '请检查相机权限或重启小程序后重试',
      showCancel: false
    });
  },

  // 按钮状态处理
  onAuthBtnTouchStart() {
    this.setData({ isAuthBtnPressed: true });
  },

  onAuthBtnTouchEnd() {
    this.setData({ isAuthBtnPressed: false });
  },

  onCaptureBtnTouchStart() {
    this.setData({ isCaptureBtnPressed: true });
  },

  onCaptureBtnTouchEnd() {
    this.setData({ isCaptureBtnPressed: false });
  },

  // 删除照片
  deletePhoto(e) {
    const { index } = e.currentTarget.dataset;
    const photos = [...this.data.photos];
    photos.splice(index, 1);
    this.setData({ photos });
  },

  // 预览照片
  previewPhoto(e) {
    const { index } = e.currentTarget.dataset;
    const urls = this.data.photos.map(photo => photo.path);
    wx.previewImage({
      current: urls[index],
      urls: urls
    });
  },

  // 上传照片
  async uploadPhotos() {
    if (this.data.isUploading) return;

    const photos = this.data.photos;
    if (!photos.length) {
      wx.showToast({
        title: '请先拍摄照片',
        icon: 'none'
      });
      return;
    }

    this.setData({ isUploading: true, uploadProgress: 0 });

    try {
      // 转换照片为base64
      const files = [];
      for (let i = 0; i < photos.length; i++) {
        const photo = photos[i];
        try {
          // 获取文件信息
          const fileInfo = await this.getFileInfo(photo.path);
          const base64 = await this.fileToBase64(photo.path);

          files.push({
            fileName: `photo_${photo.timestamp}.jpg`,
            fileId: photo.timestamp, // 使用时间戳作为文件ID
            fileSize: fileInfo.size, // 使用实际文件大小
            fileData: `data:image/jpeg;base64,${base64}` // 完整的Data URL格式
          });

          // 更新进度
          const progress = Math.floor(((i + 1) / photos.length) * 50); // 转换阶段占50%
          this.setData({ uploadProgress: progress });
        } catch (error) {
          console.error(`第${i + 1}张照片转换失败:`, error);
          throw error;
        }
      }

      // 上传数据
      const uploadData = { files };
      
      const res = await new Promise((resolve, reject) => {
        wx.request({
          url: `${apiConfig.baseUrl}${apiConfig.api.uploadPhotos}`,
          method: 'POST',
          data: uploadData,
          header: {
            'content-type': 'application/json'
          },
          success: (res) => {
            console.log('上传响应:', res);
            if (res.statusCode === 200 && res.data && res.data.code === 0) {
              // 跳转到识别结果页面并传递识别结果
              wx.navigateTo({
                url: '/pages/recognition-result/recognition-result?result=' + encodeURIComponent(JSON.stringify(res.data.data))
              });
              resolve(res);
            } else {
              console.error('上传失败，状态码:', res.statusCode);
              console.error('响应数据:', res.data);
              reject(new Error(`上传失败: ${res.statusCode} - ${JSON.stringify(res.data)}`));
            }
          },
          fail: (error) => {
            console.error('上传失败:', error);
            reject(error);
          }
        });
      });

      // 上传完成
      this.setData({ uploadProgress: 100 });

      wx.showToast({
        title: '上传成功',
        icon: 'success'
      });

      // 上传成功后清空照片列表
      this.setData({
        photos: [],
        isUploading: false,
        uploadProgress: 0
      });

    } catch (error) {
      console.error('上传失败：', error);
      wx.showToast({
        title: '上传失败，请重试',
        icon: 'error'
      });
      this.setData({ isUploading: false });
    }
  },

  // 获取文件信息
  getFileInfo(filePath) {
    return new Promise((resolve, reject) => {
      wx.getFileInfo({
        filePath: filePath,
        success: (res) => {
          resolve(res);
        },
        fail: (error) => {
          console.error('获取文件信息失败:', error);
          reject(error);
        }
      });
    });
  },

  // 文件转base64
  fileToBase64(filePath) {
    return new Promise((resolve, reject) => {
      wx.getFileSystemManager().readFile({
        filePath: filePath,
        encoding: 'base64',
        success: (res) => {
          resolve(res.data);
        },
        fail: (error) => {
          console.error('文件转base64失败:', error);
          reject(error);
        }
      });
    });
  },

  // 取消拍摄
  cancelPhotos() {
    wx.showModal({
      title: '确认取消',
      content: '确定要取消当前拍摄的照片吗？',
      success: (res) => {
        if (res.confirm) {
          this.setData({ photos: [] });
        }
      }
    });
  },


})