<!--pages/favorites/favorites.wxml-->
<navigation-bar title="收藏" back="{{true}}" color="black" background="#FFF"></navigation-bar>
<view class="container">
  <view class="favorites-header-spacer"></view>
  
  <!-- 收藏列表 -->
  <view class="favorites-list" wx:if="{{favoriteRecipes.length > 0}}">
    <view class="favorite-item" wx:for="{{favoriteRecipes}}" wx:key="id" bindtap="goToRecipeDetail" data-id="{{item.id}}">
      <text class="favorite-image">{{item.image}}</text>
      <view class="favorite-info">
        <text class="favorite-name">{{item.name}}</text>
        <view class="favorite-meta">
          <text class="favorite-time">{{item.cookTime}}分钟</text>
          <text class="favorite-difficulty">{{item.difficulty}}</text>
        </view>
      </view>
      <view class="favorite-btn" bindtap="toggleFavorite" data-id="{{item.id}}" catchtap="true">
        <text>{{item.isFavorite ? '♥' : '♡'}}</text>
      </view>
    </view>
  </view>
  
  <!-- 空状态 -->
  <view wx:else class="empty-state">
    <text class="empty-icon">♡</text>
    <text class="empty-title">暂无收藏</text>
    <text class="empty-desc">快去收藏你喜欢的菜谱吧</text>
    <button class="go-explore-btn" bindtap="goToHome">去发现美食</button>
  </view>
</view>
