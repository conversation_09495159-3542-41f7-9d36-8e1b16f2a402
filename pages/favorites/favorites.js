// pages/favorites/favorites.js
Page({

  /**
   * 页面的初始数据
   */
  data: {
    favoriteRecipes: []
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.loadFavoriteRecipes();
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 每次显示页面时重新加载收藏列表
    this.loadFavoriteRecipes();
  },

  // 加载收藏列表
  loadFavoriteRecipes() {
    // TODO: 从服务器获取收藏列表
    // 这里使用模拟数据
    const mockFavorites = [
      {
        id: 1,
        name: '清炒时蔬',
        image: '🥬',
        cookTime: 15,
        difficulty: '简单',
        isFavorite: true
      },
      {
        id: 2,
        name: '红烧排骨',
        image: '🍖',
        cookTime: 45,
        difficulty: '中等',
        isFavorite: true
      },
      {
        id: 3,
        name: '蒸蛋羹',
        image: '🥚',
        cookTime: 20,
        difficulty: '简单',
        isFavorite: true
      }
    ];
    
    this.setData({ 
      favoriteRecipes: mockFavorites 
    });
  },

  // 切换收藏状态
  toggleFavorite(e) {
    const { id } = e.currentTarget.dataset;
    const { favoriteRecipes } = this.data;
    const index = favoriteRecipes.findIndex(item => item.id === id);
    
    if (index > -1) {
      const updatedRecipes = [...favoriteRecipes];
      updatedRecipes[index].isFavorite = !updatedRecipes[index].isFavorite;
      
      // 如果取消收藏，从列表中移除
      if (!updatedRecipes[index].isFavorite) {
        updatedRecipes.splice(index, 1);
        wx.showToast({
          title: '已取消收藏',
          icon: 'success'
        });
      }
      
      this.setData({ favoriteRecipes: updatedRecipes });
      
      // TODO: 调用服务器API更新收藏状态
    }
  },

  // 跳转到菜谱详情
  goToRecipeDetail(e) {
    const { id } = e.currentTarget.dataset;
    wx.navigateTo({
      url: `/pages/recipe-detail/recipe-detail?id=${id}`,
      fail: () => {
        wx.showToast({
          title: '菜谱详情页开发中',
          icon: 'none'
        });
      }
    });
  },

  // 跳转到首页
  goToHome() {
    wx.switchTab({
      url: '/pages/index/index'
    });
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    const { favoriteRecipes } = this.data;
    let title = '我的收藏菜谱 - 智能烹饪助手';

    if (favoriteRecipes && favoriteRecipes.length > 0) {
      title = `我收藏了${favoriteRecipes.length}个美味菜谱 - 智能烹饪助手`;
    }

    return {
      title: title,
      path: '/pages/index/index',
      imageUrl: '',
      success: function(res) {
        console.log('分享成功', res);
      },
      fail: function(res) {
        console.log('分享失败', res);
      }
    };
  }
});
