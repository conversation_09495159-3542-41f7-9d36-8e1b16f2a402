// pages/settings/settings.js
const authManager = require('../../utils/auth.js');

Page({

  /**
   * 页面的初始数据
   */
  data: {
    cacheSize: '0KB'
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.calculateCacheSize();
  },

  // 计算缓存大小
  calculateCacheSize() {
    // 模拟缓存大小计算
    const mockSize = Math.floor(Math.random() * 1000) + 100;
    this.setData({
      cacheSize: `${mockSize}KB`
    });
  },

  // 编辑个人资料
  editProfile() {
    wx.navigateBack();
    // 延迟一下再触发编辑，确保页面已经返回
    setTimeout(() => {
      const pages = getCurrentPages();
      const profilePage = pages[pages.length - 1];
      if (profilePage && profilePage.showProfileEditor) {
        profilePage.showProfileEditor();
      }
    }, 300);
  },

  // 清除缓存
  clearCache() {
    wx.showModal({
      title: '清除缓存',
      content: '确定要清除应用缓存吗？',
      success: (res) => {
        if (res.confirm) {
          wx.showLoading({
            title: '清除中...'
          });
          
          // 模拟清除缓存
          setTimeout(() => {
            wx.hideLoading();
            this.setData({
              cacheSize: '0KB'
            });
            wx.showToast({
              title: '缓存已清除',
              icon: 'success'
            });
          }, 1000);
        }
      }
    });
  },

  // 检查更新
  checkUpdate() {
    wx.showLoading({
      title: '检查中...'
    });
    
    setTimeout(() => {
      wx.hideLoading();
      wx.showToast({
        title: '已是最新版本',
        icon: 'success'
      });
    }, 1000);
  },

  // 关于我们
  showAbout() {
    wx.showModal({
      title: '关于我们',
      content: '智能烹饪助手 v1.0.0\n\n一款基于AI的智能菜谱推荐应用，帮助您发现美食，享受烹饪的乐趣。',
      showCancel: false,
      confirmText: '知道了'
    });
  },

  // 意见反馈
  feedback() {
    wx.showToast({
      title: '反馈功能开发中',
      icon: 'none'
    });
  },

  // 退出登录
  logout() {
    wx.showModal({
      title: '确认退出',
      content: '确定要退出登录吗？',
      success: (res) => {
        if (res.confirm) {
          authManager.logout();
          wx.showToast({
            title: '已退出登录',
            icon: 'success'
          });
          
          // 返回到个人页面并刷新
          setTimeout(() => {
            wx.navigateBack();
          }, 1000);
        }
      }
    });
  }
});
