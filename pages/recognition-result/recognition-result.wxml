<!--pages/recognition-result/recognition-result.wxml-->
<navigation-bar title="识别结果" back="{{true}}" color="black" background="#FFF"></navigation-bar>
<view class="container">
  <!-- 添加顶部间距 -->
  <view class="header-spacer"></view>

  <!-- 识别到的食材 -->
  <view class="section">
    <!-- <view class="section-title">识别到的食材</view> -->
    <view class="ingredients-list">
      <view class="ingredient-item" wx:for="{{ingredients}}" wx:key="index">
        <view class="ingredient-check">
          <text class="check-icon">✓</text>
        </view>
        <view class="ingredient-info">
          <text class="ingredient-name">{{item.ingredientName}}</text>
        </view>
        <view class="ingredient-confidence">
          <text class="confidence-text">{{item.confidence}}%</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 查看推荐食谱按钮 -->
  <view class="action-section">
    <button class="recommend-btn" bindtap="viewRecommendedRecipes">
      查看推荐食谱 ({{ingredients.length}})
    </button>
  </view>

  <!-- 提示信息 -->
  <view class="tip-section">
    <view class="tip-content">
      <text class="tip-icon">💡</text>
      <text class="tip-text">识别结果不准确？点击食材名称可以手动修改</text>
    </view>
  </view>
</view>
