// pages/recognition-result/recognition-result.js
Page({

  /**
   * 页面的初始数据
   */
  data: {
    ingredients: []
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    console.log('识别结果页面加载，参数:', options);
    
    // 获取识别结果数据
    if (options.result) {
      try {
        const resultData = JSON.parse(decodeURIComponent(options.result));
        console.log('识别结果数据:', resultData);
        
        // 处理识别结果数据
        this.processRecognitionResult(resultData);
      } catch (error) {
        console.error('解析识别结果失败:', error);
        wx.showToast({
          title: '数据解析失败',
          icon: 'none'
        });
        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
      }
    } else {
      console.error('未获取到识别结果数据');
      wx.showToast({
        title: '未获取到识别结果',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }
  },

  /**
   * 处理识别结果数据
   */
  processRecognitionResult(resultData) {
    // 根据API返回的数据结构处理
    let ingredients = [];
    
    if (resultData && Array.isArray(resultData)) {
      // 如果返回的是数组格式
      ingredients = resultData.map((item, index) => ({
        ingredientName: item.ingredientName || item.name || `食材${index + 1}`,
        confidence: Math.round((item.confidence || 0.95) * 100)
      }));
    } else if (resultData && resultData.ingredients) {
      // 如果返回的是对象格式，包含ingredients字段
      ingredients = resultData.ingredients.map((item, index) => ({
        ingredientName: item.ingredientName || item.name || `食材${index + 1}`,
        confidence: Math.round((item.confidence || 0.95) * 100)
      }));
    } else {
      // 使用模拟数据
      ingredients = [
        { ingredientName: '西红柿', confidence: 95 },
        { ingredientName: '鸡蛋', confidence: 92 },
        { ingredientName: '大葱', confidence: 88 },
        { ingredientName: '大蒜', confidence: 85 }
      ];
    }

    this.setData({ ingredients });

    // 保存识别结果到本地存储
    this.saveRecognitionResult(ingredients);
  },

  /**
   * 保存识别结果到本地存储
   */
  saveRecognitionResult(ingredients) {
    try {
      // 获取现有的识别历史
      const recognizedIngredients = wx.getStorageSync('recognizedIngredients') || [];
      
      // 添加新的识别结果
      const newResult = {
        timestamp: new Date().getTime(),
        ingredients: ingredients,
        date: new Date().toLocaleString()
      };
      
      recognizedIngredients.unshift(newResult);
      
      // 只保留最近20条记录
      if (recognizedIngredients.length > 20) {
        recognizedIngredients.splice(20);
      }
      
      wx.setStorageSync('recognizedIngredients', recognizedIngredients);
    } catch (error) {
      console.error('保存识别结果失败:', error);
    }
  },

  /**
   * 查看推荐食谱
   */
  viewRecommendedRecipes() {
    const ingredients = this.data.ingredients;
    if (!ingredients.length) {
      wx.showToast({
        title: '没有识别到食材',
        icon: 'none'
      });
      return;
    }

    // 构造结果数据格式，与原有的result参数格式保持一致
    const resultData = ingredients.map(item => ({
      name: item.ingredientName,
      rate: `${item.confidence}%`
    }));

    // 跳转到推荐食谱页面
    wx.navigateTo({
      url: `/pages/recipe/recipe?result=${encodeURIComponent(JSON.stringify(resultData))}`
    });
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    const { ingredients } = this.data;
    let title = '智能烹饪助手 - 拍照识别食材，推荐美味菜谱';

    if (ingredients && ingredients.length > 0) {
      const ingredientNames = ingredients.map(item => item.ingredientName).join('、');
      title = `识别到${ingredientNames} - 智能烹饪助手`;
    }

    return {
      title: title,
      path: '/pages/index/index',
      imageUrl: '',
      success: function(res) {
        console.log('分享成功', res);
      },
      fail: function(res) {
        console.log('分享失败', res);
      }
    };
  }
})
