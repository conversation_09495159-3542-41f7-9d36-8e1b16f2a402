/* pages/recognition-result/recognition-result.wxss */
.container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 0 32rpx 120rpx 32rpx;
  box-sizing: border-box;
}

/* 顶部间距 */
.header-spacer {
  height: 20rpx;
}

/* 区块样式 */
.section {
  margin-bottom: 40rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 24rpx;
}

/* 食材列表样式 */
.ingredients-list {
  background: #fff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.03);
}

.ingredient-item {
  display: flex;
  align-items: center;
  padding: 32rpx 24rpx;
  border-bottom: 2rpx solid #f5f5f5;
}

.ingredient-item:last-child {
  border-bottom: none;
}

.ingredient-check {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  background: #e8f5e8;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
}

.check-icon {
  font-size: 24rpx;
  color: #4caf50;
  font-weight: bold;
}

.ingredient-info {
  flex: 1;
}

.ingredient-name {
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
}

.ingredient-confidence {
  margin-left: 16rpx;
}

.confidence-text {
  font-size: 28rpx;
  color: #4caf50;
  font-weight: bold;
}

/* 操作按钮区域 */
.action-section {
  margin: 60rpx 0 40rpx 0;
}

.recommend-btn {
  width: 100%;
  height: 88rpx;
  background: linear-gradient(90deg, #ff6b6b, #ff9a6b);
  color: #fff;
  font-size: 32rpx;
  font-weight: bold;
  border: none;
  border-radius: 44rpx;
  box-shadow: 0 4rpx 16rpx rgba(255,107,107,0.3);
  display: flex;
  align-items: center;
  justify-content: center;
}

.recommend-btn:active {
  transform: scale(0.98);
  opacity: 0.9;
}

/* 提示信息 */
.tip-section {
  margin-top: 40rpx;
}

.tip-content {
  background: #e3f2fd;
  border-radius: 16rpx;
  padding: 24rpx;
  display: flex;
  align-items: flex-start;
}

.tip-icon {
  font-size: 32rpx;
  margin-right: 16rpx;
  margin-top: 4rpx;
}

.tip-text {
  flex: 1;
  font-size: 28rpx;
  color: #1976d2;
  line-height: 1.5;
}
