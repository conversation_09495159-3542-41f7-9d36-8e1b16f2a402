// 微信小程序 AppID 配置检查工具
const fs = require('fs');
const path = require('path');

/**
 * 检查项目配置
 */
function checkProjectConfig() {
  console.log('=== 检查项目配置 ===');
  
  try {
    const configPath = path.join(__dirname, '../project.config.json');
    const configContent = fs.readFileSync(configPath, 'utf8');
    const config = JSON.parse(configContent);
    
    console.log('✅ project.config.json 文件存在');
    console.log('📱 当前 AppID:', config.appid);
    console.log('🔧 编译类型:', config.compileType);
    console.log('📚 基础库版本:', config.libVersion);
    
    // 验证 AppID 格式
    if (config.appid && config.appid.startsWith('wx') && config.appid.length === 18) {
      console.log('✅ AppID 格式正确');
    } else {
      console.log('❌ AppID 格式不正确');
    }
    
    // 检查是否为测试 AppID
    if (config.appid === 'wxbe4f9d4bae7148d2') {
      console.log('✅ AppID 与提供的信息匹配');
    } else {
      console.log('⚠️ AppID 与提供的信息不匹配');
      console.log('期望的 AppID: wxbe4f9d4bae7148d2');
    }
    
    return {
      success: true,
      appid: config.appid,
      compileType: config.compileType,
      libVersion: config.libVersion
    };
  } catch (error) {
    console.error('❌ 检查项目配置失败:', error.message);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * 检查应用配置
 */
function checkAppConfig() {
  console.log('\n=== 检查应用配置 ===');
  
  try {
    const appConfigPath = path.join(__dirname, '../app.json');
    const appConfigContent = fs.readFileSync(appConfigPath, 'utf8');
    const appConfig = JSON.parse(appConfigContent);
    
    console.log('✅ app.json 文件存在');
    console.log('📄 页面数量:', appConfig.pages.length);
    console.log('🎨 样式版本:', appConfig.style);
    console.log('🖥️ 渲染器:', appConfig.renderer);
    
    // 检查必要页面
    const requiredPages = [
      'pages/index/index',
      'pages/profile/profile'
    ];
    
    const missingPages = requiredPages.filter(page => !appConfig.pages.includes(page));
    if (missingPages.length === 0) {
      console.log('✅ 必要页面配置完整');
    } else {
      console.log('⚠️ 缺少页面:', missingPages);
    }
    
    return {
      success: true,
      pages: appConfig.pages,
      style: appConfig.style,
      renderer: appConfig.renderer
    };
  } catch (error) {
    console.error('❌ 检查应用配置失败:', error.message);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * 检查 API 配置
 */
function checkAPIConfig() {
  console.log('\n=== 检查 API 配置 ===');
  
  try {
    const apiConfigPath = path.join(__dirname, '../config/api.js');
    const apiConfigContent = fs.readFileSync(apiConfigPath, 'utf8');
    
    console.log('✅ config/api.js 文件存在');
    
    // 检查是否包含微信登录接口
    if (apiConfigContent.includes('wxLogin')) {
      console.log('✅ 微信登录接口已配置');
    } else {
      console.log('❌ 微信登录接口未配置');
    }
    
    // 检查基础 URL
    if (apiConfigContent.includes('106.55.155.115:8082')) {
      console.log('✅ 后端服务器地址已配置');
    } else {
      console.log('⚠️ 后端服务器地址可能需要更新');
    }
    
    return {
      success: true,
      hasWxLogin: apiConfigContent.includes('wxLogin'),
      hasBaseUrl: apiConfigContent.includes('106.55.155.115:8082')
    };
  } catch (error) {
    console.error('❌ 检查 API 配置失败:', error.message);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * 生成配置报告
 */
function generateReport() {
  console.log('\n🔍 开始检查微信小程序配置...\n');
  
  const projectCheck = checkProjectConfig();
  const appCheck = checkAppConfig();
  const apiCheck = checkAPIConfig();
  
  console.log('\n📊 配置检查报告');
  console.log('==================');
  
  // 项目配置状态
  console.log(`项目配置: ${projectCheck.success ? '✅ 正常' : '❌ 异常'}`);
  if (projectCheck.success) {
    console.log(`  AppID: ${projectCheck.appid}`);
    console.log(`  类型: ${projectCheck.compileType}`);
    console.log(`  基础库: ${projectCheck.libVersion}`);
  }
  
  // 应用配置状态
  console.log(`应用配置: ${appCheck.success ? '✅ 正常' : '❌ 异常'}`);
  if (appCheck.success) {
    console.log(`  页面数: ${appCheck.pages.length}`);
    console.log(`  渲染器: ${appCheck.renderer}`);
  }
  
  // API 配置状态
  console.log(`API配置: ${apiCheck.success ? '✅ 正常' : '❌ 异常'}`);
  if (apiCheck.success) {
    console.log(`  微信登录: ${apiCheck.hasWxLogin ? '✅' : '❌'}`);
    console.log(`  服务器地址: ${apiCheck.hasBaseUrl ? '✅' : '⚠️'}`);
  }
  
  // 总体状态
  const allSuccess = projectCheck.success && appCheck.success && apiCheck.success;
  console.log(`\n总体状态: ${allSuccess ? '✅ 配置正常' : '⚠️ 需要检查'}`);
  
  // 建议操作
  console.log('\n💡 建议操作:');
  if (!allSuccess) {
    console.log('1. 检查上述异常项目');
    console.log('2. 确保在微信开发者工具中使用正确的 AppID');
    console.log('3. 验证微信公众平台的小程序配置');
  } else {
    console.log('1. 在微信开发者工具中重新导入项目');
    console.log('2. 确保 AppID 设置为: wxbe4f9d4bae7148d2');
    console.log('3. 测试微信登录功能');
  }
  
  return {
    projectCheck,
    appCheck,
    apiCheck,
    allSuccess
  };
}

/**
 * 修复常见问题
 */
function fixCommonIssues() {
  console.log('\n🔧 尝试修复常见问题...');
  
  try {
    // 检查并修复 project.config.json 中的 AppID
    const configPath = path.join(__dirname, '../project.config.json');
    const configContent = fs.readFileSync(configPath, 'utf8');
    const config = JSON.parse(configContent);
    
    if (config.appid !== 'wxbe4f9d4bae7148d2') {
      console.log('🔄 更新 project.config.json 中的 AppID...');
      config.appid = 'wxbe4f9d4bae7148d2';
      fs.writeFileSync(configPath, JSON.stringify(config, null, 2));
      console.log('✅ AppID 已更新');
    } else {
      console.log('✅ AppID 已是正确值');
    }
    
    return true;
  } catch (error) {
    console.error('❌ 修复失败:', error.message);
    return false;
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  console.log('🚀 微信小程序配置检查工具');
  console.log('================================\n');
  
  // 生成报告
  const report = generateReport();
  
  // 尝试修复
  if (!report.allSuccess) {
    const fixed = fixCommonIssues();
    if (fixed) {
      console.log('\n🔄 重新检查配置...');
      generateReport();
    }
  }
  
  console.log('\n📋 下一步操作:');
  console.log('1. 打开微信开发者工具');
  console.log('2. 导入项目，AppID 设置为: wxbe4f9d4bae7148d2');
  console.log('3. 在"详情"中勾选"不校验合法域名"');
  console.log('4. 测试登录功能');
}

module.exports = {
  checkProjectConfig,
  checkAppConfig,
  checkAPIConfig,
  generateReport,
  fixCommonIssues
};
