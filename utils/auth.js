// 用户认证管理工具
const apiConfig = require('../config/api.js');

/**
 * 用户认证管理类
 */
class AuthManager {
  constructor() {
    this.userInfo = null;
    this.token = null;
    this.isLoggedIn = false;
  }

  /**
   * 微信小程序登录 - 符合官方标准流程
   * 处理身份验证和用户信息保存
   * @param {object} userInfo 从wx.getUserProfile获取的用户信息
   * @returns {Promise} 登录结果
   */
  async wxLogin(userInfo = null) {
    try {
      console.log('🚀 开始微信登录流程');
      console.log('用户信息:', userInfo);

      // 1. 获取微信登录凭证
      const loginResult = await this.getWxLoginCode();
      if (!loginResult.code) {
        throw new Error('获取微信登录凭证失败');
      }

      // 2. 调用后端登录接口（只发送code）
      const loginResponse = await this.callLoginAPI(loginResult.code);

      // 3. 保存登录信息（包含用户信息）
      await this.saveLoginInfo(loginResponse, userInfo);

      return {
        success: true,
        data: loginResponse,
        message: '登录成功'
      };
    } catch (error) {
      console.error('❌ 微信登录失败:', error);
      return {
        success: false,
        error: error.message || '登录失败',
        message: error.message || '登录失败'
      };
    }
  }

  /**
   * 保存用户信息 - 独立于登录流程
   * @param {object} userInfo 从wx.getUserProfile获取的用户信息
   */
  async saveUserInfo(userInfo) {
    try {
      console.log('💾 保存用户信息:', userInfo);

      const userProfile = {
        // 微信用户信息
        nickName: userInfo.nickName || '微信用户',
        avatarUrl: userInfo.avatarUrl || '',
        gender: userInfo.gender || 0,
        country: userInfo.country || '',
        province: userInfo.province || '',
        city: userInfo.city || '',
        language: userInfo.language || 'zh_CN',

        // 元数据
        updateTime: Date.now()
      };

      // 合并到现有用户信息中
      if (this.userInfo) {
        this.userInfo = { ...this.userInfo, ...userProfile };
      } else {
        this.userInfo = userProfile;
      }

      wx.setStorageSync('userInfo', this.userInfo);
      console.log('✅ 用户信息保存成功:', this.userInfo);

      return true;
    } catch (error) {
      console.error('❌ 保存用户信息失败:', error);
      throw error;
    }
  }

  /**
   * 获取用户信息 - 必须在用户点击事件中调用
   * @returns {Promise} 用户信息
   */
  getUserProfileInTapEvent() {
    return new Promise((resolve, reject) => {
      wx.getUserProfile({
        desc: '用于完善用户资料',
        success: (res) => {
          console.log('获取用户信息成功:', res.userInfo);
          resolve(res.userInfo);
        },
        fail: (error) => {
          console.error('获取用户信息失败:', error);
          reject(new Error(error.errMsg || '获取用户信息失败'));
        }
      });
    });
  }

  /**
   * 获取微信登录凭证
   * @returns {Promise} 登录凭证
   */
  getWxLoginCode() {
    return new Promise((resolve, reject) => {
      wx.login({
        success: (res) => {
          if (res.code) {
            console.log('获取微信登录凭证成功:', res.code);
            resolve(res);
          } else {
            console.error('获取微信登录凭证失败:', res.errMsg);
            reject(new Error(res.errMsg || '获取登录凭证失败'));
          }
        },
        fail: (error) => {
          console.error('wx.login调用失败:', error);
          reject(new Error(error.errMsg || 'wx.login调用失败'));
        }
      });
    });
  }



  /**
   * 调用后端登录接口 - 符合官方标准
   * 只发送code进行身份验证
   * @param {string} code 微信登录凭证
   * @returns {Promise} 登录响应
   */
  callLoginAPI(code) {
    return new Promise((resolve, reject) => {
      const requestData = {
        code: code
        // 注意：不发送用户信息，符合官方标准
      };

      console.log('🔐 调用登录接口，请求数据:', requestData);

      wx.request({
        url: `${apiConfig.baseUrl}${apiConfig.api.wxLogin}`,
        method: 'POST',
        data: requestData,
        header: {
          'content-type': 'application/json'
        },
        success: (res) => {
          console.log('登录接口响应:', res);

          if (res.statusCode === 200 && res.data && res.data.code === 0) {
            // 后端只返回验证结果和token，不返回用户信息
            const backendData = res.data.data;
            console.log('后端验证成功，返回数据:', backendData);
            resolve(backendData);
          } else {
            const errorMsg = res.data && res.data.msg ? res.data.msg : '登录失败';
            console.error('后端登录验证失败:', errorMsg);
            reject(new Error(errorMsg));
          }
        },
        fail: (error) => {
          console.error('登录接口调用失败:', error);
          reject(new Error('网络错误，请重试'));
        }
      });
    });
  }

  /**
   * 保存登录信息到本地存储 - 包含认证信息和用户信息
   * @param {object} loginData 后端登录返回的数据
   * @param {object} userInfo 微信用户信息
   */
  async saveLoginInfo(loginData, userInfo = null) {
    try {
      console.log('💾 保存登录信息，后端数据:', loginData);
      console.log('微信用户信息:', userInfo);

      // 构建完整的用户信息对象
      const completeUserInfo = {
        // 微信用户信息（优先使用）
        nickName: userInfo ? (userInfo.nickName || '微信用户') : '微信用户',
        avatarUrl: userInfo ? (userInfo.avatarUrl || '') : '',
        gender: userInfo ? (userInfo.gender || 0) : 0,
        country: userInfo ? (userInfo.country || '') : '',
        province: userInfo ? (userInfo.province || '') : '',
        city: userInfo ? (userInfo.city || '') : '',
        language: userInfo ? (userInfo.language || 'zh_CN') : 'zh_CN',

        // 后端认证信息
        openId: loginData.openId || loginData.openid || '',
        sessionKey: loginData.sessionKey || '',
        userId: loginData.userId || null,

        // 登录时间
        loginTime: Date.now()
      };

      // 保存完整的用户信息
      this.userInfo = completeUserInfo;
      wx.setStorageSync('userInfo', completeUserInfo);
      console.log('✅ 完整用户信息已保存:', completeUserInfo);

      // 保存token和后端相关信息
      if (loginData.token) {
        this.token = loginData.token;
        wx.setStorageSync('token', loginData.token);
        console.log('✅ Token已保存');
      }

      // 保存openId
      if (loginData.openId || loginData.openid) {
        const openId = loginData.openId || loginData.openid;
        wx.setStorageSync('openId', openId);
        console.log('✅ OpenId已保存:', openId);
      }

      // 保存sessionKey
      if (loginData.sessionKey) {
        wx.setStorageSync('sessionKey', loginData.sessionKey);
        console.log('✅ SessionKey已保存');
      }

      // 保存用户ID
      if (loginData.userId) {
        wx.setStorageSync('userId', loginData.userId);
        console.log('✅ 用户ID已保存:', loginData.userId);
      } else {
        console.warn('⚠️  警告：后端登录接口未返回userId，这可能会影响需要userId的功能（如历史记录）');
        console.log('后端返回的完整数据:', loginData);
      }

      // 保存过期时间
      if (loginData.expireTime) {
        wx.setStorageSync('tokenExpireTime', loginData.expireTime);
        console.log('✅ Token过期时间已保存:', new Date(loginData.expireTime));
      }

      // 保存登录状态
      this.isLoggedIn = true;
      wx.setStorageSync('isLoggedIn', true);

      console.log('🎉 登录信息保存完成！用户信息基于微信真实数据');
    } catch (error) {
      console.error('保存登录信息失败:', error);
      throw new Error('保存登录信息失败');
    }
  }

  /**
   * 从本地存储恢复登录状态
   */
  restoreLoginState() {
    try {
      const isLoggedIn = wx.getStorageSync('isLoggedIn');
      const userInfo = wx.getStorageSync('userInfo');
      const token = wx.getStorageSync('token');
      const userId = wx.getStorageSync('userId');
      const openId = wx.getStorageSync('openId');
      const tokenExpireTime = wx.getStorageSync('tokenExpireTime');

      console.log('🔄 恢复登录状态，本地数据:', {
        isLoggedIn,
        hasUserInfo: !!userInfo,
        hasToken: !!token,
        userId,
        openId: openId ? '已存在' : '不存在',
        tokenExpireTime: tokenExpireTime ? new Date(tokenExpireTime) : '无'
      });

      // 检查token是否过期
      if (tokenExpireTime && Date.now() > tokenExpireTime) {
        console.log('⏰ Token已过期，清除登录状态');
        this.logout();
        return false;
      }

      if (isLoggedIn && userInfo && token) {
        this.isLoggedIn = true;
        this.userInfo = userInfo;
        this.token = token;
        console.log('✅ 登录状态恢复成功');
        console.log('👤 用户信息:', {
          nickName: userInfo.nickName,
          avatarUrl: userInfo.avatarUrl ? '已设置' : '未设置',
          hasRealInfo: userInfo.nickName !== '微信用户'
        });
        return true;
      } else {
        console.log('❌ 无有效的登录状态');
        return false;
      }
    } catch (error) {
      console.error('❌ 恢复登录状态失败:', error);
      return false;
    }
  }

  /**
   * 退出登录
   */
  logout() {
    try {
      // 清除内存中的数据
      this.userInfo = null;
      this.token = null;
      this.isLoggedIn = false;

      // 清除本地存储
      wx.removeStorageSync('userInfo');
      wx.removeStorageSync('token');
      wx.removeStorageSync('isLoggedIn');
      wx.removeStorageSync('userId');
      wx.removeStorageSync('openId');
      wx.removeStorageSync('sessionKey');
      wx.removeStorageSync('tokenExpireTime');

      console.log('🚪 退出登录成功，所有数据已清除');
      return true;
    } catch (error) {
      console.error('❌ 退出登录失败:', error);
      return false;
    }
  }

  /**
   * 检查登录状态
   * @returns {boolean} 是否已登录
   */
  checkLoginStatus() {
    return this.isLoggedIn && this.userInfo && this.token;
  }

  /**
   * 获取当前用户信息
   * @returns {object|null} 用户信息
   */
  getCurrentUser() {
    return this.userInfo;
  }

  /**
   * 更新用户信息
   * @param {object} userInfo 新的用户信息
   * @returns {boolean} 是否更新成功
   */
  updateUserInfo(userInfo) {
    if (!userInfo) {
      console.error('更新用户信息失败: 无效的用户信息');
      return false;
    }
    
    try {
      // 更新内存中的用户信息
      this.userInfo = userInfo;
      
      // 更新本地存储
      wx.setStorageSync('userInfo', userInfo);
      
      console.log('✅ 用户信息已更新:', userInfo);
      return true;
    } catch (error) {
      console.error('更新用户信息失败:', error);
      return false;
    }
  }

  /**
   * 获取当前token
   * @returns {string|null} token
   */
  getToken() {
    return this.token;
  }

  /**
   * 获取带认证头的请求配置
   * @returns {object} 请求头配置
   */
  getAuthHeaders() {
    const headers = {
      'content-type': 'application/json'
    };

    if (this.token) {
      headers['Authorization'] = `Bearer ${this.token}`;
    }

    return headers;
  }
}

// 创建全局实例
const authManager = new AuthManager();

// 应用启动时恢复登录状态
authManager.restoreLoginState();

module.exports = authManager;