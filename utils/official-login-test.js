// 符合官方标准的登录流程测试工具
const authManager = require('./auth.js');

/**
 * 测试官方标准登录流程
 */
async function testOfficialLoginFlow() {
  console.log('=== 测试官方标准登录流程 ===');
  
  try {
    // 第一步：身份验证登录（只发送code）
    console.log('1. 🔐 开始身份验证...');
    const loginResult = await authManager.wxLogin();
    
    if (!loginResult.success) {
      throw new Error('身份验证失败: ' + loginResult.message);
    }
    
    console.log('✅ 身份验证成功');
    console.log('认证结果:', loginResult.data);
    
    // 第二步：模拟获取用户信息
    console.log('2. 👤 模拟获取用户信息...');
    const mockUserInfo = {
      nickName: "张三",
      avatarUrl: "https://thirdwx.qlogo.cn/mmopen/vi_32/real_avatar/132",
      gender: 1,
      country: "中国",
      province: "广东省",
      city: "深圳市",
      language: "zh_CN"
    };
    
    // 第三步：保存用户信息
    console.log('3. 💾 保存用户信息...');
    await authManager.saveUserInfo(mockUserInfo);
    
    // 第四步：验证最终结果
    console.log('4. 🔍 验证最终结果...');
    const finalUserInfo = authManager.getCurrentUser();
    const isLoggedIn = authManager.checkLoginStatus();
    
    console.log('最终用户信息:', finalUserInfo);
    console.log('登录状态:', isLoggedIn);
    
    // 验证数据完整性
    const hasAuth = !!(finalUserInfo.openId && finalUserInfo.userId);
    const hasUserInfo = !!(finalUserInfo.nickName && finalUserInfo.avatarUrl);
    
    console.log('5. ✅ 验证结果:');
    console.log(`   - 认证信息: ${hasAuth ? '✅' : '❌'}`);
    console.log(`   - 用户信息: ${hasUserInfo ? '✅' : '❌'}`);
    console.log(`   - 登录状态: ${isLoggedIn ? '✅' : '❌'}`);
    
    return {
      success: hasAuth && hasUserInfo && isLoggedIn,
      userInfo: finalUserInfo,
      hasAuth,
      hasUserInfo,
      isLoggedIn
    };
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * 对比官方流程与之前的实现
 */
function compareLoginFlows() {
  console.log('\n=== 官方标准流程 vs 之前实现 ===');
  
  console.log('📊 流程对比:');
  console.log('');
  console.log('❌ 之前的实现（不符合官方标准）:');
  console.log('   1. wx.getUserProfile() 获取用户信息');
  console.log('   2. 发送 code + userInfo 给后端');
  console.log('   3. 后端返回 token + 用户信息');
  console.log('   4. 前端使用后端返回的用户信息');
  console.log('   问题：混合了身份验证和用户信息获取');
  console.log('');
  console.log('✅ 官方标准流程:');
  console.log('   1. wx.login() 获取 code');
  console.log('   2. 发送 code 给后端进行身份验证');
  console.log('   3. 后端返回 openId + sessionKey + token');
  console.log('   4. wx.getUserProfile() 获取用户信息');
  console.log('   5. 前端直接使用微信用户信息');
  console.log('   优势：职责分离，符合官方设计');
  console.log('');
  console.log('🎯 关键改进:');
  console.log('   - 身份验证和用户信息获取分离');
  console.log('   - 后端只处理身份验证');
  console.log('   - 前端完全管理用户信息');
  console.log('   - 符合微信官方设计理念');
}

/**
 * 测试数据分离
 */
function testDataSeparation() {
  console.log('\n=== 测试数据分离 ===');
  
  try {
    const userInfo = authManager.getCurrentUser();
    
    if (!userInfo) {
      console.log('❌ 没有用户信息');
      return { success: false, error: '没有用户信息' };
    }
    
    console.log('📊 数据结构分析:');
    
    // 认证相关数据
    const authData = {
      openId: userInfo.openId,
      sessionKey: userInfo.sessionKey,
      userId: userInfo.userId,
      loginTime: userInfo.loginTime
    };
    
    // 用户信息数据
    const profileData = {
      nickName: userInfo.nickName,
      avatarUrl: userInfo.avatarUrl,
      gender: userInfo.gender,
      country: userInfo.country,
      province: userInfo.province,
      city: userInfo.city,
      language: userInfo.language,
      updateTime: userInfo.updateTime
    };
    
    console.log('🔐 认证数据:', authData);
    console.log('👤 用户信息:', profileData);
    
    // 验证数据完整性
    const hasAuthData = Object.values(authData).some(v => v);
    const hasProfileData = Object.values(profileData).some(v => v);
    
    console.log('📈 数据完整性:');
    console.log(`   - 认证数据: ${hasAuthData ? '✅' : '❌'}`);
    console.log(`   - 用户信息: ${hasProfileData ? '✅' : '❌'}`);
    
    return {
      success: hasAuthData && hasProfileData,
      authData,
      profileData,
      hasAuthData,
      hasProfileData
    };
    
  } catch (error) {
    console.error('❌ 测试数据分离失败:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * 测试本地存储结构
 */
function testStorageStructure() {
  console.log('\n=== 测试本地存储结构 ===');
  
  try {
    const userInfo = wx.getStorageSync('userInfo');
    const token = wx.getStorageSync('token');
    const isLoggedIn = wx.getStorageSync('isLoggedIn');
    
    console.log('📦 本地存储内容:');
    console.log('   - userInfo:', userInfo ? '已存储' : '未存储');
    console.log('   - token:', token ? '已存储' : '未存储');
    console.log('   - isLoggedIn:', isLoggedIn);
    
    if (userInfo) {
      console.log('👤 用户信息详情:');
      console.log(`   - 昵称: ${userInfo.nickName || '未设置'}`);
      console.log(`   - 头像: ${userInfo.avatarUrl ? '已设置' : '未设置'}`);
      console.log(`   - OpenId: ${userInfo.openId ? '已设置' : '未设置'}`);
      console.log(`   - 用户ID: ${userInfo.userId || '未设置'}`);
      
      // 判断数据来源
      const isRealData = userInfo.nickName && 
                        userInfo.nickName !== '微信用户' && 
                        userInfo.avatarUrl;
      
      console.log(`   - 数据类型: ${isRealData ? '✅ 真实微信数据' : '⚠️ 默认数据'}`);
    }
    
    return {
      success: !!(userInfo && token),
      userInfo,
      hasToken: !!token,
      isLoggedIn
    };
    
  } catch (error) {
    console.error('❌ 检查本地存储失败:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * 运行所有测试
 */
async function runAllTests() {
  console.log('🧪 开始官方标准登录流程测试\n');
  
  const results = [];
  
  // 1. 对比登录流程
  compareLoginFlows();
  
  // 2. 测试官方登录流程
  const loginTest = await testOfficialLoginFlow();
  results.push({ name: '官方登录流程', ...loginTest });
  
  // 3. 测试数据分离
  const separationTest = testDataSeparation();
  results.push({ name: '数据分离', ...separationTest });
  
  // 4. 测试本地存储结构
  const storageTest = testStorageStructure();
  results.push({ name: '本地存储结构', ...storageTest });
  
  // 输出测试结果
  console.log('\n📊 测试结果汇总:');
  results.forEach((result, index) => {
    const status = result.success ? '✅' : '❌';
    console.log(`${index + 1}. ${status} ${result.name}`);
    if (!result.success && result.error) {
      console.log(`   错误: ${result.error}`);
    }
  });
  
  const successCount = results.filter(r => r.success).length;
  const totalCount = results.length;
  
  console.log(`\n🎯 测试完成: ${successCount}/${totalCount} 通过`);
  
  if (successCount === totalCount) {
    console.log('\n🎉 所有测试通过！');
    console.log('💡 现在的实现符合微信官方标准');
    console.log('🚀 用户应该能看到真实的微信昵称和头像');
  } else {
    console.log('\n⚠️ 部分测试失败，需要进一步调试');
  }
  
  return {
    success: successCount === totalCount,
    results: results,
    summary: `${successCount}/${totalCount} 测试通过`
  };
}

module.exports = {
  testOfficialLoginFlow,
  compareLoginFlows,
  testDataSeparation,
  testStorageStructure,
  runAllTests
};
