// 前端主导的登录流程测试工具
const authManager = require('./auth.js');

/**
 * 测试前端主导的登录流程
 */
async function testFrontendLoginFlow() {
  console.log('=== 测试前端主导的登录流程 ===');
  
  // 1. 模拟微信用户授权获取的真实信息
  const mockWxUserInfo = {
    nickName: "张三",
    avatarUrl: "https://thirdwx.qlogo.cn/mmopen/vi_32/real_user_avatar_123/132",
    gender: 1,
    country: "中国",
    province: "广东省",
    city: "深圳市",
    language: "zh_CN"
  };
  
  console.log('1. 📱 微信用户授权信息:', mockWxUserInfo);
  
  // 2. 模拟后端只返回验证结果和token（不包含用户信息）
  const mockBackendResponse = {
    userId: 123,
    token: "eyJhbGciOiJIUzUxMiJ9.real_jwt_token",
    openId: "on3U95Bntn51lW_QQ4PZvm2y1GHc",
    sessionKey: "session_key_123",
    expireTime: Date.now() + 7 * 24 * 60 * 60 * 1000
  };
  
  console.log('2. 🔐 后端验证响应（仅认证信息）:', mockBackendResponse);
  
  try {
    // 3. 模拟保存登录信息（前端主导）
    await authManager.saveLoginInfo(mockBackendResponse, mockWxUserInfo);
    console.log('3. ✅ 登录信息保存完成');
    
    // 4. 验证保存的用户信息
    const savedUserInfo = authManager.getCurrentUser();
    console.log('4. 👤 最终用户信息:', savedUserInfo);
    
    // 5. 验证关键字段
    console.log('5. 🔍 关键字段验证:');
    console.log(`   - 昵称: ${savedUserInfo.nickName} ${savedUserInfo.nickName === mockWxUserInfo.nickName ? '✅' : '❌'}`);
    console.log(`   - 头像: ${savedUserInfo.avatarUrl === mockWxUserInfo.avatarUrl ? '✅' : '❌'}`);
    console.log(`   - 性别: ${savedUserInfo.gender === mockWxUserInfo.gender ? '✅' : '❌'}`);
    console.log(`   - 地区: ${savedUserInfo.country}-${savedUserInfo.province}-${savedUserInfo.city}`);
    console.log(`   - OpenId: ${savedUserInfo.openId ? '✅' : '❌'}`);
    console.log(`   - Token: ${authManager.getToken() ? '✅' : '❌'}`);
    
    // 6. 验证登录状态
    const isLoggedIn = authManager.checkLoginStatus();
    console.log('6. 🔑 登录状态:', isLoggedIn ? '✅ 已登录' : '❌ 未登录');
    
    return {
      success: true,
      userInfo: savedUserInfo,
      isLoggedIn: isLoggedIn
    };
  } catch (error) {
    console.error('❌ 测试失败:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * 测试本地存储数据结构
 */
function testLocalStorageStructure() {
  console.log('\n=== 测试本地存储数据结构 ===');
  
  try {
    const userInfo = wx.getStorageSync('userInfo');
    const token = wx.getStorageSync('token');
    const openId = wx.getStorageSync('openId');
    const sessionKey = wx.getStorageSync('sessionKey');
    const userId = wx.getStorageSync('userId');
    const isLoggedIn = wx.getStorageSync('isLoggedIn');
    
    console.log('📦 本地存储数据:');
    console.log('   - userInfo:', userInfo);
    console.log('   - token:', token ? '已存储' : '未存储');
    console.log('   - openId:', openId ? '已存储' : '未存储');
    console.log('   - sessionKey:', sessionKey ? '已存储' : '未存储');
    console.log('   - userId:', userId);
    console.log('   - isLoggedIn:', isLoggedIn);
    
    // 验证用户信息结构
    if (userInfo) {
      console.log('\n👤 用户信息结构验证:');
      console.log(`   - 昵称: ${userInfo.nickName || '未设置'}`);
      console.log(`   - 头像: ${userInfo.avatarUrl ? '已设置' : '未设置'}`);
      console.log(`   - 性别: ${userInfo.gender === 1 ? '男' : userInfo.gender === 2 ? '女' : '未知'}`);
      console.log(`   - 地区: ${userInfo.country || '未知'}`);
      console.log(`   - 登录时间: ${userInfo.loginTime ? new Date(userInfo.loginTime) : '未记录'}`);
      
      const isRealData = userInfo.nickName !== '微信用户' && userInfo.avatarUrl;
      console.log(`   - 数据类型: ${isRealData ? '✅ 真实微信数据' : '⚠️ 默认数据'}`);
    }
    
    return {
      success: true,
      hasUserInfo: !!userInfo,
      hasToken: !!token,
      hasOpenId: !!openId,
      isComplete: !!(userInfo && token && openId)
    };
  } catch (error) {
    console.error('❌ 检查本地存储失败:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * 测试登录状态恢复
 */
function testLoginStateRestore() {
  console.log('\n=== 测试登录状态恢复 ===');
  
  try {
    // 清除当前内存状态
    authManager.userInfo = null;
    authManager.token = null;
    authManager.isLoggedIn = false;
    
    console.log('1. 🧹 已清除内存状态');
    
    // 尝试恢复登录状态
    const restored = authManager.restoreLoginState();
    console.log('2. 🔄 恢复结果:', restored ? '✅ 成功' : '❌ 失败');
    
    if (restored) {
      const userInfo = authManager.getCurrentUser();
      console.log('3. 👤 恢复的用户信息:', {
        nickName: userInfo.nickName,
        avatarUrl: userInfo.avatarUrl ? '已设置' : '未设置',
        hasRealInfo: userInfo.nickName !== '微信用户'
      });
    }
    
    return {
      success: restored,
      userInfo: authManager.getCurrentUser()
    };
  } catch (error) {
    console.error('❌ 恢复登录状态失败:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * 对比新旧登录流程
 */
function compareLoginFlows() {
  console.log('\n=== 新旧登录流程对比 ===');
  
  console.log('📊 流程对比:');
  console.log('');
  console.log('❌ 旧流程（依赖后端用户信息）:');
  console.log('   1. 微信授权获取用户信息');
  console.log('   2. 发送code和用户信息给后端');
  console.log('   3. 后端返回token和用户信息');
  console.log('   4. 前端使用后端返回的用户信息 ← 问题所在');
  console.log('   5. 显示默认的"微信用户"');
  console.log('');
  console.log('✅ 新流程（前端主导用户信息）:');
  console.log('   1. 微信授权获取用户信息');
  console.log('   2. 发送code给后端验证');
  console.log('   3. 后端返回token和openId');
  console.log('   4. 前端直接使用微信真实用户信息 ← 关键改进');
  console.log('   5. 显示真实的用户昵称和头像');
  console.log('');
  console.log('🎯 核心改进:');
  console.log('   - 用户信息完全由前端管理');
  console.log('   - 后端只负责身份验证');
  console.log('   - 确保显示真实的微信用户信息');
}

/**
 * 运行所有测试
 */
async function runAllTests() {
  console.log('🧪 开始前端主导登录流程测试\n');
  
  const results = [];
  
  // 1. 对比登录流程
  compareLoginFlows();
  
  // 2. 测试前端登录流程
  const loginTest = await testFrontendLoginFlow();
  results.push({ name: '前端登录流程', ...loginTest });
  
  // 3. 测试本地存储结构
  const storageTest = testLocalStorageStructure();
  results.push({ name: '本地存储结构', ...storageTest });
  
  // 4. 测试登录状态恢复
  const restoreTest = testLoginStateRestore();
  results.push({ name: '登录状态恢复', ...restoreTest });
  
  // 输出测试结果
  console.log('\n📊 测试结果汇总:');
  results.forEach((result, index) => {
    const status = result.success ? '✅' : '❌';
    console.log(`${index + 1}. ${status} ${result.name}`);
    if (!result.success && result.error) {
      console.log(`   错误: ${result.error}`);
    }
  });
  
  const successCount = results.filter(r => r.success).length;
  const totalCount = results.length;
  
  console.log(`\n🎯 测试完成: ${successCount}/${totalCount} 通过`);
  
  if (successCount === totalCount) {
    console.log('\n🎉 所有测试通过！前端主导的登录流程工作正常');
    console.log('💡 现在用户登录后应该能看到真实的微信昵称和头像');
  } else {
    console.log('\n⚠️ 部分测试失败，需要进一步调试');
  }
  
  return {
    success: successCount === totalCount,
    results: results,
    summary: `${successCount}/${totalCount} 测试通过`
  };
}

module.exports = {
  testFrontendLoginFlow,
  testLocalStorageStructure,
  testLoginStateRestore,
  compareLoginFlows,
  runAllTests
};
