// 登录数据处理测试工具
const authManager = require('./auth.js');

/**
 * 测试后端返回的登录数据处理
 */
function testLoginDataProcessing() {
  console.log('=== 测试登录数据处理 ===');
  
  // 模拟后端返回的数据结构
  const mockBackendResponse = {
    "code": 0,
    "msg": "成功",
    "data": {
      "userId": 1,
      "token": "eyJhbGciOiJIUzUxMiJ9.eyJvcGVuaWQiOiJvbjNVOTVCbnRuNTFsV19RUTRQWnZtMnkxR0hjIiwibmlja25hbWUiOiLlvq7kv6HnlKjmiLciLCJ1c2VySWQiOjEsInN1YiI6IjEiLCJpc3MiOiJjb29rLWFwcCIsImlhdCI6MTc1MjI5OTA0NSwiZXhwIjoxNzUyOTAzODQ1fQ.K9YstcTEYUO_S6gm3vQIOunkS10LeGOsNUaIcdztMiVFT6yfIf5sN4JbrfrAZkDSXzb1Yy_wk6IoeUTD4-Q6Tw",
      "expireTime": 1752903846130,
      "userInfo": {
        "id": 1,
        "nickname": "微信用户",
        "avatarUrl": "https://thirdwx.qlogo.cn/mmopen/vi_32/POgEwh4mIHO4nibH0KlMECNjjGxQUq24ZEaGT4poC6icRiccVGKSyXwibcPq4BWmiaIGuG1icwxaQX6grC9VemZoJ8rg/132",
        "gender": 0,
        "status": 0,
        "lastLoginTime": 1752299045852,
        "createTime": 1752299045852
      }
    }
  };

  console.log('1. 模拟后端响应数据:', JSON.stringify(mockBackendResponse, null, 2));

  try {
    // 模拟认证管理器处理登录数据
    const loginData = mockBackendResponse.data;
    
    console.log('2. 提取的登录数据:', loginData);
    
    // 测试保存登录信息
    authManager.saveLoginInfo(loginData).then(() => {
      console.log('3. 登录信息保存完成');
      
      // 测试获取用户信息
      const savedUserInfo = authManager.getCurrentUser();
      console.log('4. 保存后获取的用户信息:', savedUserInfo);
      
      // 测试登录状态检查
      const isLoggedIn = authManager.checkLoginStatus();
      console.log('5. 登录状态检查:', isLoggedIn);
      
      // 测试本地存储
      const storageUserInfo = wx.getStorageSync('userInfo');
      const storageToken = wx.getStorageSync('token');
      const storageUserId = wx.getStorageSync('userId');
      
      console.log('6. 本地存储验证:');
      console.log('   - userInfo:', storageUserInfo);
      console.log('   - token:', storageToken ? '已存储' : '未存储');
      console.log('   - userId:', storageUserId);
      
      // 验证数据格式转换
      if (savedUserInfo) {
        console.log('7. 数据格式验证:');
        console.log('   - nickName:', savedUserInfo.nickName);
        console.log('   - avatarUrl:', savedUserInfo.avatarUrl);
        console.log('   - 原始nickname:', savedUserInfo.nickname);
        console.log('   - 数据完整性:', savedUserInfo.nickName && savedUserInfo.avatarUrl ? '✅' : '❌');
      }
      
      return {
        success: true,
        userInfo: savedUserInfo,
        isLoggedIn: isLoggedIn
      };
    }).catch(error => {
      console.error('测试失败:', error);
      return {
        success: false,
        error: error.message
      };
    });
    
  } catch (error) {
    console.error('测试过程出错:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * 测试数据格式转换
 */
function testDataFormatConversion() {
  console.log('\n=== 测试数据格式转换 ===');
  
  const backendUserInfo = {
    "id": 1,
    "nickname": "微信用户",
    "avatarUrl": "https://thirdwx.qlogo.cn/mmopen/vi_32/POgEwh4mIHO4nibH0KlMECNjjGxQUq24ZEaGT4poC6icRiccVGKSyXwibcPq4BWmiaIGuG1icwxaQX6grC9VemZoJ8rg/132",
    "gender": 0,
    "status": 0,
    "lastLoginTime": 1752299045852,
    "createTime": 1752299045852
  };
  
  console.log('后端用户信息格式:', backendUserInfo);
  
  // 转换为前端期望的格式
  const frontendUserInfo = {
    id: backendUserInfo.id,
    nickName: backendUserInfo.nickname || '微信用户',
    avatarUrl: backendUserInfo.avatarUrl || '',
    gender: backendUserInfo.gender || 0,
    // 保留原始的后端用户信息
    ...backendUserInfo
  };
  
  console.log('转换后的前端格式:', frontendUserInfo);
  
  // 验证转换结果
  const isValid = frontendUserInfo.nickName && frontendUserInfo.avatarUrl;
  console.log('转换结果验证:', isValid ? '✅ 成功' : '❌ 失败');
  
  return {
    success: isValid,
    original: backendUserInfo,
    converted: frontendUserInfo
  };
}

/**
 * 测试页面数据绑定
 */
function testPageDataBinding() {
  console.log('\n=== 测试页面数据绑定 ===');
  
  // 模拟页面setData操作
  const mockPageData = {
    userInfo: null,
    isLoading: false
  };
  
  console.log('初始页面数据:', mockPageData);
  
  // 模拟登录成功后的数据更新
  const userInfo = authManager.getCurrentUser();
  if (userInfo) {
    mockPageData.userInfo = userInfo;
    mockPageData.isLoading = false;
    
    console.log('更新后的页面数据:', mockPageData);
    
    // 验证WXML绑定的关键字段
    console.log('WXML绑定验证:');
    console.log('  - {{userInfo.avatarUrl}}:', mockPageData.userInfo.avatarUrl);
    console.log('  - {{userInfo.nickName}}:', mockPageData.userInfo.nickName);
    console.log('  - wx:if="{{userInfo}}":', !!mockPageData.userInfo);
    
    return {
      success: true,
      pageData: mockPageData
    };
  } else {
    console.log('❌ 无用户信息，页面绑定将失败');
    return {
      success: false,
      error: '无用户信息'
    };
  }
}

/**
 * 运行所有测试
 */
async function runAllTests() {
  console.log('🧪 开始运行登录数据处理测试\n');
  
  const results = [];
  
  // 1. 测试数据格式转换
  const conversionTest = testDataFormatConversion();
  results.push({ name: '数据格式转换', ...conversionTest });
  
  // 2. 测试登录数据处理
  const processingTest = testLoginDataProcessing();
  results.push({ name: '登录数据处理', success: true }); // 异步操作，暂时标记为成功
  
  // 等待一下让异步操作完成
  await new Promise(resolve => setTimeout(resolve, 100));
  
  // 3. 测试页面数据绑定
  const bindingTest = testPageDataBinding();
  results.push({ name: '页面数据绑定', ...bindingTest });
  
  // 输出测试结果
  console.log('\n📊 测试结果汇总:');
  results.forEach((result, index) => {
    const status = result.success ? '✅' : '❌';
    console.log(`${index + 1}. ${status} ${result.name}`);
    if (!result.success && result.error) {
      console.log(`   错误: ${result.error}`);
    }
  });
  
  const successCount = results.filter(r => r.success).length;
  const totalCount = results.length;
  
  console.log(`\n🎯 测试完成: ${successCount}/${totalCount} 通过`);
  
  if (successCount === totalCount) {
    console.log('\n💡 建议: 登录数据处理正常，检查页面是否正确调用了 checkLoginStatus()');
  } else {
    console.log('\n⚠️ 建议: 存在问题，需要进一步调试');
  }
  
  return {
    success: successCount === totalCount,
    results: results,
    summary: `${successCount}/${totalCount} 测试通过`
  };
}

module.exports = {
  testLoginDataProcessing,
  testDataFormatConversion,
  testPageDataBinding,
  runAllTests
};
