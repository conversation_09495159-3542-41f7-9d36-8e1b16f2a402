// 用户信息调试工具
const authManager = require('./auth.js');

/**
 * 调试微信用户信息获取
 */
function debugUserInfo() {
  console.log('=== 微信用户信息调试工具 ===');
  
  console.log('📱 当前登录状态:', authManager.checkLoginStatus());
  console.log('👤 当前用户信息:', authManager.getCurrentUser());
  
  // 检查存储的用户信息
  const storedUserInfo = wx.getStorageSync('userInfo');
  console.log('💾 存储的用户信息:', storedUserInfo);
  
  // 检查微信授权信息
  const openId = wx.getStorageSync('openId');
  const sessionKey = wx.getStorageSync('sessionKey');
  const token = wx.getStorageSync('token');
  
  console.log('🔑 授权信息:');
  console.log('  OpenId:', openId);
  console.log('  SessionKey:', sessionKey ? '已保存' : '未保存');
  console.log('  Token:', token ? '已保存' : '未保存');
}

/**
 * 测试微信用户信息获取
 */
function testGetUserProfile() {
  console.log('=== 测试微信用户信息获取 ===');
  
  wx.getUserProfile({
    desc: '用于调试用户信息获取',
    success: (res) => {
      console.log('✅ 获取用户信息成功:');
      console.log('  昵称:', res.userInfo.nickName);
      console.log('  头像:', res.userInfo.avatarUrl);
      console.log('  性别:', res.userInfo.gender);
      console.log('  国家:', res.userInfo.country);
      console.log('  省份:', res.userInfo.province);
      console.log('  城市:', res.userInfo.city);
      console.log('  语言:', res.userInfo.language);
      
      // 判断是否为测试数据
      if (res.userInfo.nickName === '微信用户' && res.userInfo.avatarUrl.includes('thirdwx.qlogo.cn')) {
        console.log('⚠️  注意：这是微信开发者工具的测试数据');
        console.log('   在真机上才能获取到真实的用户信息');
      } else {
        console.log('✅ 这可能是真实的用户信息');
      }
    },
    fail: (error) => {
      console.error('❌ 获取用户信息失败:', error);
    }
  });
}

/**
 * 模拟真实用户信息
 */
function simulateRealUserInfo() {
  console.log('=== 模拟真实用户信息 ===');
  
  const mockRealUserInfo = {
    nickName: '张三',
    avatarUrl: 'https://thirdwx.qlogo.cn/mmopen/vi_32/real_avatar/132',
    gender: 1,
    country: '中国',
    province: '广东省',
    city: '深圳市',
    language: 'zh_CN'
  };
  
  console.log('模拟真实用户信息:', mockRealUserInfo);
  
  // 保存模拟信息
  authManager.saveUserInfo(mockRealUserInfo);
  console.log('✅ 模拟用户信息已保存');
  
  // 检查保存结果
  const currentUser = authManager.getCurrentUser();
  console.log('当前用户信息:', currentUser);
}

/**
 * 清除测试数据
 */
function clearTestData() {
  console.log('=== 清除测试数据 ===');
  
  wx.removeStorageSync('userInfo');
  wx.removeStorageSync('token');
  wx.removeStorageSync('openId');
  wx.removeStorageSync('sessionKey');
  wx.removeStorageSync('userId');
  wx.removeStorageSync('isLoggedIn');
  
  console.log('✅ 测试数据已清除');
}

/**
 * 运行所有调试测试
 */
function runAllDebugTests() {
  console.log('=== 开始运行所有调试测试 ===');
  
  // 1. 调试当前状态
  debugUserInfo();
  
  console.log('\n');
  
  // 2. 测试用户信息获取
  console.log('请在用户点击事件中调用 testGetUserProfile()');
  console.log('例如：在登录按钮的点击事件中调用');
  
  console.log('\n');
  
  // 3. 提供测试建议
  console.log('🧪 测试建议:');
  console.log('1. 在微信开发者工具中测试：会显示测试数据');
  console.log('2. 在真机上测试：会显示真实用户信息');
  console.log('3. 使用模拟数据：调用 simulateRealUserInfo()');
  console.log('4. 清除数据：调用 clearTestData()');
}

// 导出调试函数
module.exports = {
  debugUserInfo,
  testGetUserProfile,
  simulateRealUserInfo,
  clearTestData,
  runAllDebugTests
};
