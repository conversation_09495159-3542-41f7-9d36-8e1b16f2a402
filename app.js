// app.js
const authManager = require('./utils/auth.js');

App({
  onLaunch() {
    console.log('小程序启动');

    // 恢复登录状态
    const hasLogin = authManager.restoreLoginState();
    if (hasLogin) {
      console.log('用户已登录:', authManager.getCurrentUser());
    } else {
      console.log('用户未登录');
    }
  },

  onShow() {
    console.log('小程序显示');
  },

  onHide() {
    console.log('小程序隐藏');
  },

  // 全局数据
  globalData: {
    authManager: authManager
  }
})
