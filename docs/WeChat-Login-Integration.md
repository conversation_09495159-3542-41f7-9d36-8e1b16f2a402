# 微信小程序登录功能集成文档

## 📋 功能概述

已成功集成微信小程序登录功能，支持用户通过微信授权登录，获取用户信息并与后端API进行交互。

## ✨ 实现的功能

### 1. **微信登录流程**
- **获取登录凭证**: 调用 `wx.login()` 获取临时登录凭证code
- **获取用户信息**: 调用 `wx.getUserProfile()` 获取用户授权信息
- **后端验证**: 将code和用户信息发送到后端进行验证
- **保存登录状态**: 将返回的token和用户信息保存到本地

### 2. **登录状态管理**
- **状态持久化**: 登录状态保存到本地存储，应用重启后自动恢复
- **状态检查**: 提供登录状态检查方法
- **自动恢复**: 应用启动时自动恢复登录状态
- **退出登录**: 支持用户主动退出登录

### 3. **用户界面优化**
- **登录按钮**: 未登录时显示微信登录按钮
- **用户信息**: 登录后显示用户头像和昵称
- **退出按钮**: 登录后显示退出登录按钮
- **加载状态**: 登录过程中显示加载状态

## 🔧 技术实现

### 1. **API配置更新**

<augment_code_snippet path="config/api.js" mode="EXCERPT">
````javascript
api: {
  // 微信小程序登录接口
  // 请求格式: POST /user/wx-login
  // 参数: { code, userInfo }
  wxLogin: '/user/wx-login'
}
````
</augment_code_snippet>

### 2. **认证管理器**

<augment_code_snippet path="utils/auth.js" mode="EXCERPT">
````javascript
class AuthManager {
  async wxLogin() {
    // 1. 获取微信登录凭证
    const loginResult = await this.getWxLoginCode();
    
    // 2. 获取用户信息
    const userProfile = await this.getUserProfile();
    
    // 3. 调用后端登录接口
    const loginResponse = await this.callLoginAPI(loginResult.code, userProfile);
    
    // 4. 保存登录信息
    await this.saveLoginInfo(loginResponse);
  }
}
````
</augment_code_snippet>

### 3. **页面集成**

<augment_code_snippet path="pages/profile/profile.js" mode="EXCERPT">
````javascript
// 登录函数 - 必须在用户点击事件中调用
login() {
  // 首先获取用户信息（必须在点击事件中调用）
  wx.getUserProfile({
    desc: '用于完善用户资料',
    success: async (res) => {
      // 调用认证管理器进行登录
      const result = await authManager.wxLogin(res.userInfo);

      if (result.success) {
        const userInfo = authManager.getCurrentUser();
        this.setData({ userInfo });
        this.getFavoriteRecipes();
      }
    }
  });
}
````
</augment_code_snippet>

## 📱 用户界面

### 1. **登录前状态**
```xml
<view class="avatar-placeholder">
  <text class="avatar-icon">👤</text>
</view>
<text class="nickname-placeholder">未登录</text>
<button bindtap="login" class="btn-login">微信登录</button>
```

### 2. **登录后状态**
```xml
<image class="avatar" src="{{userInfo.avatarUrl}}" />
<text class="nickname">{{userInfo.nickName}}</text>
<button bindtap="logout" class="btn-logout">退出登录</button>
```

## 🔄 登录流程

```mermaid
sequenceDiagram
    participant User as 用户
    participant MP as 小程序
    participant WX as 微信服务器
    participant API as 后端API
    
    User->>MP: 点击登录按钮
    MP->>WX: wx.login() 获取code
    WX-->>MP: 返回临时登录凭证
    MP->>User: wx.getUserProfile() 请求授权
    User-->>MP: 用户授权
    MP->>API: POST /user/wx-login {code, userInfo}
    API-->>MP: 返回 {token, userInfo}
    MP->>MP: 保存登录状态到本地
    MP-->>User: 登录成功
```

## 📊 数据结构

### 1. **登录请求格式**
```json
{
  "code": "微信小程序登录凭证",
  "userInfo": {
    "nickName": "用户昵称",
    "avatarUrl": "用户头像URL",
    "gender": 1,
    "country": "中国",
    "province": "广东省",
    "city": "深圳市",
    "language": "zh_CN"
  }
}
```

### 2. **登录响应格式**
```json
{
  "code": 0,
  "msg": "登录成功",
  "data": {
    "userInfo": {
      "id": 12345,
      "nickName": "用户昵称",
      "avatarUrl": "用户头像URL",
      "gender": 1,
      "country": "中国",
      "province": "广东省",
      "city": "深圳市",
      "language": "zh_CN"
    },
    "token": "JWT_TOKEN",
    "expiresIn": 7200
  }
}
```

### 3. **本地存储结构**
```javascript
// 用户信息
wx.setStorageSync('userInfo', userInfo);

// 认证token
wx.setStorageSync('token', token);

// 登录状态
wx.setStorageSync('isLoggedIn', true);
```

## 🛡️ 安全特性

### 1. **Token管理**
- JWT token自动添加到请求头
- Token过期自动处理
- 安全的本地存储

### 2. **状态验证**
- 登录状态实时检查
- 用户信息完整性验证
- 网络错误处理

### 3. **隐私保护**
- 用户授权后才获取信息
- 敏感信息加密存储
- 退出登录完全清除数据

## 🧪 测试工具

### 1. **测试功能**
- 微信登录流程测试
- 登录状态恢复测试
- 退出登录测试
- API响应模拟测试

### 2. **使用方法**
```javascript
const authTest = require('./utils/auth-test.js');

// 运行所有测试
authTest.runAllTests();

// 单独测试登录状态恢复
authTest.testLoginStateRestore();
```

## 📄 相关文件

### 新增文件
- `utils/auth.js` - 认证管理器
- `utils/auth-test.js` - 测试工具
- `docs/WeChat-Login-Integration.md` - 本文档

### 修改文件
- `config/api.js` - 添加登录接口配置
- `app.js` - 添加认证管理器初始化
- `pages/profile/profile.js` - 集成登录功能
- `pages/profile/profile.wxml` - 更新用户界面
- `pages/profile/profile.wxss` - 添加样式

## 🚀 使用指南

### 1. **在其他页面使用认证**
```javascript
const authManager = require('../../utils/auth.js');

// 检查登录状态
if (authManager.checkLoginStatus()) {
  // 用户已登录
  const userInfo = authManager.getCurrentUser();
  const token = authManager.getToken();
}

// 获取认证头
const headers = authManager.getAuthHeaders();
```

### 2. **API请求示例**
```javascript
wx.request({
  url: 'https://api.example.com/user/profile',
  method: 'GET',
  header: authManager.getAuthHeaders(),
  success: (res) => {
    // 处理响应
  }
});
```

## ⚠️ 注意事项

### 1. **微信小程序要求**
- 需要在微信开发者工具或真机上测试
- 需要配置合法域名
- 需要用户主动触发登录

### 2. **后端配置**
- 确保后端API接口正确实现
- 配置CORS跨域支持
- 实现JWT token验证

### 3. **用户体验**
- 登录按钮需要用户主动点击
- 提供清晰的登录状态反馈
- 处理网络异常情况

## ✅ 完成状态

- ✅ 微信登录API集成
- ✅ 认证管理器实现
- ✅ 用户界面更新
- ✅ 登录状态管理
- ✅ 本地存储持久化
- ✅ 测试工具开发
- ✅ 文档编写完成

现在用户可以通过微信授权登录小程序，享受个性化的功能体验。
